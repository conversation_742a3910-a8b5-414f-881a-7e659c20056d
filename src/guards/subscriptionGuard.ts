import type { NavigationGuard } from 'vue-router';
import { useSubscriptionStore } from '@/stores/subscription.ts';
import { useAuthStore } from '@/stores/auth.ts';
import { useToast } from 'primevue/usetoast';


/**
 * Performs subscription-status checks
 * @param to
 * @param from
 * @param next
 */
export const subscriptionStatusGuard: NavigationGuard = async (to, from, next) => {
    const subscriptionStore = useSubscriptionStore();
    const authStore = useAuthStore();
    const toast = useToast();

    if (!authStore.isLoggedIn() || to.meta.doesNotRequireActiveSubscription) {
        // Route does not require having an active subscription, just continue
        next();
    } else {
        // Fetch subscription status
        await subscriptionStore.dispatchGetSubscription();
        if (subscriptionStore.isActive()) {
            next();
        } else {
            toast.add({
                severity: 'warn',
                summary: 'Suscripción inactiva',
                detail: 'Revisa pagos pendientes',
                life: 5000
            });
            // Redirect to subscription summary page where user can
            // see and pay invoices to get the subscription re-activated
            next('/subscription');
        }
    }
};
