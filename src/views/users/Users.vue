<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth.ts';
import UsersList from './UsersList.vue';
import UserCreate from './UserCreate.vue';

const authStore = useAuthStore();
const showCreateForm = ref(false);
const usersListRef = ref();

function openCreateForm() {
    showCreateForm.value = true;
}

function closeCreateForm() {
    showCreateForm.value = false;
}

function onUserCreated() {
    showCreateForm.value = false;
    // Refresh the users list
    if (usersListRef.value) {
        usersListRef.value.refreshList();
    }
}
</script>
<template>
    <div>
        <div class="card">
            <Toolbar class="mb-6">
                <template #end>
                    <Button
                        label="New User"
                        icon="pi pi-plus"
                        severity="secondary"
                        class="mr-2"
                        v-if="authStore.hasPrivilege('USER_CREATE')"
                        @click="openCreateForm"
                    />
                </template>
            </Toolbar>

            <Dialog
                v-model:visible="showCreateForm"
                :header="t('createNewUser')"
                :modal="true"
                :style="{ width: '50rem' }"
                @hide="closeCreateForm"
            >
                <UserCreate
                    @user-created="onUserCreated"
                    @cancel="closeCreateForm"
                />
            </Dialog>

            <UsersList ref="usersListRef" />
        </div>
    </div>
</template>
