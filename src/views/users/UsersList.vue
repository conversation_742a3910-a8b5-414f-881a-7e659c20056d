<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { useUsersStore } from '@/stores/users';
import { useAuthStore } from '@/stores/auth';
import { storeToRefs } from 'pinia';
import { GetUsersInput, User } from '@/services/users/types';
import { formatDate } from '@/utilities/DateUtils';

import { useToast } from 'primevue/usetoast';
import { useTranslation } from "i18next-vue";

const { t } = useTranslation();

// Stores
const usersStore = useUsersStore();
const authStore = useAuthStore();
const { users, totalElements } = storeToRefs(usersStore);
const toast = useToast();

// Refs
const dt = ref();
const loading = ref(false);

// Dialog state
const userDetailsVisible = ref(false);
const selectedUser = ref<User | null>(null);

// Pagination
const first = ref(0);
const rows = ref(10);
const currentPage = computed(() => Math.floor(first.value / rows.value) + 1);

// Filtering
const usernameFilter = ref<string | null>(null);

// Sorting
const sortField = ref<string | undefined>(undefined);
const sortOrder = ref<number | undefined>(undefined);

// Load users on component mount
onMounted(async () => {
    await loadUsers();
});

// Load users function
async function loadUsers() {
    loading.value = true;

    const input: GetUsersInput = {
        pageSize: rows.value,
        pageNumber: currentPage.value,
        usernameFilter: usernameFilter.value || undefined,
        sortBy: sortField.value,
        sortDirection: sortOrder.value === 1 ? 'ASC' : sortOrder.value === -1 ? 'DESC' : undefined
    };

    const result = await usersStore.dispatchGetUsers(input);
    loading.value = false;
}

// Event handlers
function onPage(event: any) {
    first.value = event.first;
    rows.value = event.rows;
    loadUsers();
}

function onSort(event: any) {
    sortField.value = event.sortField;
    sortOrder.value = event.sortOrder;
    loadUsers();
}

function onFilter() {
    first.value = 0; // Reset to first page when filtering
    loadUsers();
}

// Expose refresh method for parent component
function refreshList() {
    loadUsers();
}

// Expose the refresh method
defineExpose({
    refreshList
});

function getRoleLabelColor(role: string): string {
    switch (role) {
        case 'ADMIN':
            return 'success';
        case 'OWNER':
            return 'secondary';
        case 'EMPLOYEE':
            return 'info';
        default:
            return 'info';
    }
}

function editUser(user: User) {
    // TODO: Implement edit user functionality
    toast.add({
        severity: 'info',
        summary: 'Edit User',
        detail: `Edit functionality for ${user.username} to be implemented`,
        life: 3000
    });
}

function onRowClick(event: any) {
    selectedUser.value = event.data;
    userDetailsVisible.value = true;
}

function hideUserDetails() {
    userDetailsVisible.value = false;
    selectedUser.value = null;
}

</script>

<template>
    <div>

            <DataTable
                ref="dt"
                :value="users"
                dataKey="id"
                :paginator="true"
                :rows="rows"
                :first="first"
                :totalRecords="totalElements"
                :loading="loading"
                :lazy="true"
                :sortField="sortField"
                :sortOrder="sortOrder"
                selectionMode="single"
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                :rowsPerPageOptions="[5, 10, 25, 50]"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} users"
                @page="onPage"
                @sort="onSort"
                @row-click="onRowClick"
            >
                <template #header>
                    <div class="flex flex-wrap gap-2 items-center justify-between">
                        <h4 class="m-0">{{ t('manageUsers') }}</h4>
                        <IconField>
                            <InputIcon>
                                <i class="pi pi-search" />
                            </InputIcon>
                            <InputText v-model="usernameFilter" :placeholder="t('searchByUsername')" @input="onFilter" />
                        </IconField>
                    </div>
                </template>

                <Column style="width: 5rem" :exportable="false">
                    <template #body="slotProps">
                        <Avatar
                            v-if="slotProps.data.profilePictureUrl"
                            :image="slotProps.data.profilePictureUrl"
                            shape="circle"
                            size="normal"
                        />
                        <Avatar
                            v-else
                            :label="slotProps.data.username.charAt(0).toUpperCase()"
                            shape="circle"
                            size="normal"
                        />
                    </template>
                </Column>

                <Column field="username" :header="t('email')" sortable style="min-width: 16rem">
                    <template #body="slotProps">
                        <span class="font-medium">{{ slotProps.data.username }}</span>
                    </template>
                </Column>

                <Column field="createDate" :header="t('createDate')" sortable style="min-width: 12rem">
                    <template #body="slotProps">
                        <span>{{ formatDate(slotProps.data.createDate) }}</span>
                    </template>
                </Column>

                <Column field="role" :header="t('role')" style="min-width: 10rem">
                    <template #body="slotProps">
                        <Tag :value="slotProps.data.role" :severity="getRoleLabelColor(slotProps.data.role)" />
                    </template>
                </Column>

                <Column :exportable="false" style="min-width: 8rem">
                    <template #body="slotProps">
                        <Button icon="pi pi-pencil" outlined rounded class="mr-2" v-if="authStore.hasPrivilege('USER_UPDATE')" @click="editUser(slotProps.data)" />
                        <Button
                            icon="pi pi-trash"
                            outlined
                            rounded
                            severity="danger"
                            v-if="authStore.hasPrivilege('USER_DELETE')"
                            @click="() => toast.add({ severity: 'info', summary: 'Delete User', detail: 'Delete functionality to be implemented', life: 3000 })"
                        />
                    </template>
                </Column>
            </DataTable>

        <!-- User Details Dialog -->
        <Dialog
            v-model:visible="userDetailsVisible"
            :header="t('userDetails')"
            :modal="true"
            :style="{ width: '50rem' }"
            @hide="hideUserDetails"
        >
            <div v-if="selectedUser" class="flex flex-col gap-4">
                <!-- User Avatar and Basic Info -->
                <div class="flex items-center gap-4 pb-4 border-b border-surface-200">
                    <Avatar
                        v-if="selectedUser.profilePictureUrl"
                        :image="selectedUser.profilePictureUrl"
                        shape="circle"
                        size="xlarge"
                    />
                    <Avatar
                        v-else
                        :label="selectedUser.username.charAt(0).toUpperCase()"
                        shape="circle"
                        size="xlarge"
                    />
                    <div>
                        <h3 class="m-0 text-xl font-semibold">{{ selectedUser.username }}</h3>
                        <Tag :value="selectedUser.role" :severity="getRoleLabelColor(selectedUser.role)" class="mt-2" />
                    </div>
                </div>

                <!-- User Details -->
                <div>
                    <label class="block text-sm font-medium text-surface-600 mb-1">{{ t('createDate') }}</label>
                    <p class="text-surface-900">{{ formatDate(selectedUser.createDate) }}</p>
                </div>

                <!-- Privileges -->
                <div v-if="selectedUser.privileges && selectedUser.privileges.length > 0">
                    <label class="block text-sm font-medium text-surface-600 mb-2">{{ t('privileges') }}</label>
                    <div class="flex flex-wrap gap-2">
                        <Tag
                            v-for="privilege in selectedUser.privileges"
                            :key="privilege"
                            :value="t(privilege)"
                            severity="info"
                            class="text-xs"
                        />
                    </div>
                </div>
            </div>

            <template #footer>
                <Button
                    :label="t('close')"
                    icon="pi pi-times"
                    @click="hideUserDetails"
                    class="p-button-text"
                />
                <Button
                    v-if="authStore.hasPrivilege('USER_UPDATE')"
                    :label="t('edit')"
                    icon="pi pi-pencil"
                    @click="editUser(selectedUser!)"
                    severity="secondary"
                />
            </template>
        </Dialog>
    </div>
</template>
